# Best Sellers 自动补位功能使用说明

## 功能概述

Best Sellers 模块现在支持产品缺货或下架时的自动补位功能，确保始终显示足够数量的可用产品。

## 如何使用

### 1. 启用自动补位功能

在 Shopify 后台编辑 Best Sellers 模块时：

1. 找到 **"Enable Auto Replacement"** 选项
2. 勾选此选项以启用自动补位功能
3. 默认情况下此功能是启用的

### 2. 配置备用产品集合（可选）

1. 找到 **"Fallback Collection"** 选项
2. 选择一个产品集合作为备用产品来源
3. 如果留空，系统将从所有产品中选择备用产品

### 3. 配置产品

正常配置您想要展示的产品：

1. 添加产品卡片块
2. 选择产品
3. 配置 Sale 标签和折扣显示
4. 不用担心产品缺货问题，系统会自动处理

## 工作原理

### 自动检测
- 系统自动检测产品是否可用（有库存且已发布）
- 过滤掉缺货或下架的产品

### 智能补位
- 优先显示您手动配置的可用产品
- 当产品数量不足时，从备用集合自动补充
- 确保不会重复显示同一产品

### 保持一致性
- 补位产品使用相同的显示格式
- 自动显示 Sale 标签（如果产品有折扣）
- 保持轮播和响应式功能正常工作

## 使用场景

### 电商运营
- **季节性产品**：当季节性产品下架时自动补充其他产品
- **库存管理**：缺货产品自动被替换，避免显示空白
- **新品上线**：新产品可以自动填补空缺位置

### 营销活动
- **促销结束**：促销产品下架后自动显示其他产品
- **限时优惠**：优惠结束的产品自动被替换
- **热门推荐**：始终保持推荐区域饱满

## 配置建议

### 备用集合选择
- **热门产品集合**：选择销量好的产品作为备用
- **新品集合**：用新品填补空缺
- **特定分类**：选择与主要产品相关的分类
- **全部产品**：让系统从所有产品中选择（默认）

### 最佳实践
1. **定期检查**：定期检查配置的产品状态
2. **合理分类**：为备用集合选择合适的产品分类
3. **测试功能**：在不同场景下测试自动补位效果
4. **监控性能**：观察页面加载速度是否受影响

## 注意事项

- 补位产品会自动显示 Sale 标签（如果有折扣）
- 补位产品使用默认设置，不会应用自定义折扣标签
- 系统会检查所有备用产品以确保找到足够数量
- 功能完全向后兼容，不影响现有配置

## 故障排除

### 如果补位不工作
1. 检查是否启用了 "Enable Auto Replacement"
2. 确认备用集合中有足够的可用产品
3. 检查产品是否已发布且有库存

### 如果显示重复产品
- 这种情况不应该发生，系统有防重复机制
- 如果出现，请检查产品 ID 是否正确

### 如果页面加载慢
- 考虑选择较小的备用产品集合
- 确保备用集合中的产品都是高质量的

## 技术支持

如果遇到问题，请检查：
1. Shopify 后台的模块配置
2. 产品的发布状态和库存
3. 集合的产品数量和状态
