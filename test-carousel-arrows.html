<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carousel Arrows Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .category-shop {
            background: #FFFFFF;
            padding: 64px 100px;
        }
        
        .category-shop .section-title {
            font-size: 1.354vw; /* 26px based on 1920px */
            line-height: 100%;
            margin-bottom: 30px;
            font-family: Playfair Display;
            font-weight: 400;
        }

        .category-shop .carousel-wrapper {
            position: relative;
            display: flex;
            align-items: flex-start;
        }

        .category-shop .carousel-viewport {
            overflow: hidden;
            width: calc(4 * 20.677vw + 3 * 2.292vw); /* 4个产品 + 3个间距 */
            flex: 1;
            margin: 0 auto; /* 居中显示轮播区域 */
        }

        .category-shop .carousel-container {
            display: flex;
            align-items: flex-start;
            transition: transform 0.3s ease;
        }

        .category-shop .carousel-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            position: absolute;
            top: 10.339vw; /* 图片高度一半 */
            background: transparent;
            width: 2.083vw; /* 40px */
            height: 2.083vw; /* 40px */
            transform: translateY(-50%); /* 垂直居中 */
            border: 2px solid #6D4C41;
            border-radius: 50%;
        }

        .category-shop .carousel-arrow:hover {
            background: #6D4C41;
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .category-shop .carousel-arrow.prev {
            left: calc(50% - calc(4 * 20.677vw + 3 * 2.292vw) / 2 + 10.339vw - 1.042vw);
        }

        .category-shop .carousel-arrow.next {
            left: calc(50% + calc(4 * 20.677vw + 3 * 2.292vw) / 2 - 10.339vw - 1.042vw);
        }

        .category-shop .product-card {
            flex: 0 0 20.677vw; /* 397px based on 1920px */
            text-align: center;
            position: relative;
            margin-right: 2.292vw; /* 44px based on 1920px */
        }

        .category-shop .product-card:last-child {
            margin-right: 0;
        }

        .category-shop .product-card img {
            width: 20.677vw; /* 397px based on 1920px */
            height: 20.677vw; /* 397px based on 1920px */
            background: #f0f0f0;
            border: 1px solid #ddd;
            object-fit: cover;
        }

        /* 添加一些辅助线来验证位置 */
        .debug-line {
            position: absolute;
            background: red;
            z-index: 5;
        }
        
        .debug-line.vertical {
            width: 2px;
            height: 100vh;
            top: 0;
        }
        
        .debug-line.horizontal {
            height: 2px;
            width: 100vw;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="category-shop">
        <div class="section-title">Shop by Category - Arrow Position Test</div>
        <div class="carousel-wrapper">
            <button class="carousel-arrow prev">←</button>
            <div class="carousel-viewport">
                <div class="carousel-container">
                    <div class="product-card">
                        <img src="https://via.placeholder.com/397x397/cccccc/666666?text=Product+1" alt="Product 1">
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/397x397/cccccc/666666?text=Product+2" alt="Product 2">
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/397x397/cccccc/666666?text=Product+3" alt="Product 3">
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/397x397/cccccc/666666?text=Product+4" alt="Product 4">
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/397x397/cccccc/666666?text=Product+5" alt="Product 5">
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/397x397/cccccc/666666?text=Product+6" alt="Product 6">
                    </div>
                </div>
            </div>
            <button class="carousel-arrow next">→</button>
        </div>
    </div>

    <!-- 调试辅助线 -->
    <div class="debug-line vertical" style="left: calc(50% - calc(4 * 20.677vw + 3 * 2.292vw) / 2 + 10.339vw);"></div>
    <div class="debug-line vertical" style="left: calc(50% + calc(4 * 20.677vw + 3 * 2.292vw) / 2 - 10.339vw);"></div>
    <div class="debug-line horizontal" style="top: calc(64px + 30px + 10.339vw);"></div>

    <script>
        // 简单的轮播功能测试
        const container = document.querySelector('.carousel-container');
        const prevBtn = document.querySelector('.carousel-arrow.prev');
        const nextBtn = document.querySelector('.carousel-arrow.next');
        let currentIndex = 0;
        const maxVisible = 4;
        const totalItems = 6;

        function updateCarousel() {
            const cardWidth = window.innerWidth * 0.20677; // 20.677vw
            const gap = window.innerWidth * 0.02292; // 2.292vw
            const itemWidth = cardWidth + gap;
            const translateX = -currentIndex * itemWidth;
            container.style.transform = `translateX(${translateX}px)`;
            
            prevBtn.disabled = currentIndex === 0;
            nextBtn.disabled = currentIndex >= totalItems - maxVisible;
        }

        prevBtn.addEventListener('click', () => {
            if (currentIndex > 0) {
                currentIndex--;
                updateCarousel();
            }
        });

        nextBtn.addEventListener('click', () => {
            if (currentIndex < totalItems - maxVisible) {
                currentIndex++;
                updateCarousel();
            }
        });

        updateCarousel();
    </script>
</body>
</html>
