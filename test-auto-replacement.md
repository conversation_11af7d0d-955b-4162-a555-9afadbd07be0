# Best Sellers 自动补位功能测试

## 功能说明

为 best-sellers.liquid 添加了产品缺货或下架时的自动补位功能。

## 主要特性

### 1. 产品状态检测
- 使用 `product.available` 检查产品是否可用
- 使用 `product.published_at != blank` 检查产品是否已发布
- 自动过滤掉缺货或下架的产品

### 2. 自动补位逻辑
- 首先显示配置的可用产品
- 当可用产品不足时，从备用集合自动补充
- 目标显示数量：8个产品
- 避免重复显示同一产品

### 3. 配置选项
- **Enable Auto Replacement**: 开启/关闭自动补位功能
- **Fallback Collection**: 指定备用产品集合（可选，留空则使用所有产品）

### 4. 补位规则
- 优先显示手动配置的可用产品
- 补位产品自动显示Sale标签（如果有折扣）
- 补位产品使用默认设置（无自定义折扣标签）
- 检查备用集合中的所有产品直到找到足够数量

## 测试场景

### 场景1：正常情况
- 所有配置的产品都可用
- 应该正常显示配置的产品
- 不触发自动补位

### 场景2：部分产品缺货
- 部分配置产品缺货或下架
- 应该只显示可用的配置产品
- 如果启用自动补位，会从备用集合补充

### 场景3：大部分产品不可用
- 大部分配置产品不可用
- 自动从备用集合补充足够的产品
- 确保显示足够数量的产品

### 场景4：关闭自动补位
- 禁用自动补位功能
- 只显示配置的可用产品
- 不从备用集合补充

## 技术实现

### 产品状态检查
```liquid
{% if product != blank and product.available and product.published_at != blank %}
  <!-- 产品可用 -->
{% endif %}
```

### 重复检查
```liquid
{% assign product_id_str = product.id | append: '' %}
{% unless displayed_product_ids contains product_id_str %}
  <!-- 产品未重复 -->
{% endunless %}
```

### 数据属性
- `data-product-id`: 产品ID
- `data-fallback="true"`: 标识补位产品

## 兼容性

- 保持原有轮播功能不变
- 保持原有响应式设计不变
- 保持原有样式和交互不变
- 向后兼容，不影响现有配置

## 注意事项

1. 补位产品使用默认Sale标签设置
2. 检查备用集合中的所有产品以确保找到足够数量
3. 使用字符串操作避免Liquid数组限制
4. 补位产品可通过CSS进行视觉区分（可选）

## 更新记录

- 移除了备用产品检查数量限制
- 现在会检查备用集合中的所有产品直到找到足够数量
- 提高了补位成功率
